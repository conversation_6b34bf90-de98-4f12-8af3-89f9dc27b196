2025-06-26 12:10:35,090 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\test_combined_dataset_20250626_121035.log
2025-06-26 12:10:35,091 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 12:10:35,091 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 12:10:35,094 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 12:10:35,097 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 12:10:35,097 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 12:10:35,098 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 12:10:35,098 - common.data_loader - INFO - 健康样本数量设置为-1，自动匹配故障样本数量: 3
2025-06-26 12:10:35,098 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 12:10:35,098 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 12:10:35,098 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:10:35,098 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:10:35,098 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:10:35,098 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:10:35,099 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:10:35,099 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:10:35,099 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 12:10:35,099 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:10:35,104 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 12:10:35,109 - common.data_loader - INFO - 数据加载完成:
2025-06-26 12:10:35,109 - common.data_loader - INFO -   训练样本: 24
2025-06-26 12:10:35,110 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 12:10:35,110 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 12:10:35,110 - common.data_loader - INFO -   类别数: 8
2025-06-26 12:10:35,115 - main - INFO - 健康样本处理逻辑:
2025-06-26 12:10:35,115 - main - INFO -   只生成故障样本: False
2025-06-26 12:10:35,115 - main - INFO -   使用真实健康样本: True
2025-06-26 12:10:35,115 - main - INFO -   真实健康样本数量配置: -1
2025-06-26 12:10:35,115 - main - INFO -   检测到生成的健康样本，使用生成的健康样本
