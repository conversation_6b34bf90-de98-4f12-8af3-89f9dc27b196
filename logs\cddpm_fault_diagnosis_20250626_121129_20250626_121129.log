2025-06-26 12:11:29,684 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\cddpm_fault_diagnosis_20250626_121129_20250626_121129.log
2025-06-26 12:11:29,685 - __main__ - INFO - ================================================================================
2025-06-26 12:11:29,685 - __main__ - INFO - 开始单一实验: cddpm_fault_diagnosis
2025-06-26 12:11:29,685 - __main__ - INFO - 数据集: KAT
2025-06-26 12:11:29,685 - __main__ - INFO - ================================================================================
2025-06-26 12:11:29,685 - __main__ - INFO - 🚀 实验开始
2025-06-26 12:11:29,685 - __main__ - INFO - ================================================================================
2025-06-26 12:11:29,685 - __main__ - INFO - 当前实验配置
2025-06-26 12:11:29,685 - __main__ - INFO - ================================================================================
2025-06-26 12:11:29,685 - __main__ - INFO - 数据集: KAT
2025-06-26 12:11:29,685 - __main__ - INFO - 故障样本每类: [3]
2025-06-26 12:11:29,686 - __main__ - INFO - 健康样本总数: -1
2025-06-26 12:11:29,686 - __main__ - INFO - 信号长度: 1024
2025-06-26 12:11:29,686 - __main__ - INFO - 归一化方法: minmax
2025-06-26 12:11:29,686 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 12:11:29,686 - __main__ - INFO - 每类生成样本数: [3]
2025-06-26 12:11:29,686 - __main__ - INFO - 只生成故障样本: False
2025-06-26 12:11:29,686 - __main__ - INFO - 扩散模型训练轮数: 10
2025-06-26 12:11:29,686 - __main__ - INFO - 分类器训练轮数: 100
2025-06-26 12:11:29,686 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 12:11:29,686 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 12:11:29,686 - __main__ - INFO - 设备: auto
2025-06-26 12:11:29,687 - __main__ - INFO - 性能模式: high_performance
2025-06-26 12:11:29,687 - __main__ - INFO - 随机种子: 42
2025-06-26 12:11:29,687 - __main__ - INFO - ================================================================================
2025-06-26 12:11:29,687 - __main__ - INFO - ============================================================
2025-06-26 12:11:29,687 - __main__ - INFO - 健康样本配置验证
2025-06-26 12:11:29,687 - __main__ - INFO - ============================================================
2025-06-26 12:11:29,687 - __main__ - INFO - 扩散训练健康样本数量: -1
2025-06-26 12:11:29,687 - __main__ - INFO - 只生成故障样本: False
2025-06-26 12:11:29,687 - __main__ - INFO - 分类器使用真实健康样本: True
2025-06-26 12:11:29,687 - __main__ - INFO - 真实健康样本数量: -1
2025-06-26 12:11:29,687 - __main__ - INFO - ✅ 健康样本配置验证通过
2025-06-26 12:11:29,688 - __main__ - INFO - ============================================================
2025-06-26 12:11:29,688 - __main__ - INFO - 使用设备: cuda
2025-06-26 12:11:29,689 - __main__ - INFO - 加载数据...
2025-06-26 12:11:29,689 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 12:11:29,689 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 12:11:29,692 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 12:11:29,696 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 12:11:29,696 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 12:11:29,696 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 12:11:29,696 - common.data_loader - INFO - 健康样本数量设置为-1，自动匹配故障样本数量: 3
2025-06-26 12:11:29,696 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 12:11:29,696 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 12:11:29,696 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:11:29,697 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:11:29,697 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:11:29,697 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:11:29,697 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:11:29,697 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:11:29,697 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 12:11:29,697 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:11:29,702 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 12:11:29,707 - common.data_loader - INFO - 数据加载完成:
2025-06-26 12:11:29,708 - common.data_loader - INFO -   训练样本: 24
2025-06-26 12:11:29,708 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 12:11:29,708 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 12:11:29,708 - common.data_loader - INFO -   类别数: 8
2025-06-26 12:11:29,710 - __main__ - INFO - ==================================================
2025-06-26 12:11:29,710 - __main__ - INFO - 开始训练数据增强模型
2025-06-26 12:11:29,710 - __main__ - INFO - ==================================================
2025-06-26 12:11:29,711 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-26 12:11:29,711 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 12:11:29,712 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 12:11:29,712 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 12:11:29,712 - models.cddpm - INFO -   类别数量: 8
2025-06-26 12:11:29,921 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 12:11:29,921 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 12:11:29,922 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 12:11:29,922 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 12:11:30,259 - __main__ - INFO - 数据增强方法已初始化: CDDPM
2025-06-26 12:11:30,259 - models.augmentation_factory - INFO - 开始训练 CDDPM 模型...
2025-06-26 12:11:30,260 - models.augmentation_factory - INFO - 开始训练CDDPM模型，共10轮
2025-06-26 12:12:09,915 - models.augmentation_factory - INFO - Epoch   1/10: Train Loss: 0.835234, Val Loss: 0.853776, Weighted Loss: 0.840797 (Best✓)
2025-06-26 12:12:13,145 - models.augmentation_factory - INFO - Epoch   3/10: Train Loss: 0.819184, Val Loss: 0.802814, Weighted Loss: 0.814273 (Best✓)
2025-06-26 12:12:14,298 - models.augmentation_factory - INFO - Epoch   4/10: Train Loss: 0.810327, Val Loss: 0.810471, Weighted Loss: 0.810370 (Best✓)
2025-06-26 12:12:15,282 - models.augmentation_factory - INFO - Epoch   5/10: Train Loss: 0.811718, Val Loss: 0.805387, Weighted Loss: 0.809818 (Best✓)
2025-06-26 12:12:16,301 - models.augmentation_factory - INFO - Epoch   6/10: Train Loss: 0.810913, Val Loss: 0.794510, Weighted Loss: 0.805992 (Best✓)
2025-06-26 12:12:17,128 - models.augmentation_factory - INFO - Epoch   7/10: Train Loss: 0.803462, Val Loss: 0.798220, Weighted Loss: 0.801889 (Best✓)
2025-06-26 12:12:18,796 - models.augmentation_factory - INFO - Epoch   9/10: Train Loss: 0.797677, Val Loss: 0.810866, Weighted Loss: 0.801634 (Best✓)
2025-06-26 12:12:20,005 - models.augmentation_factory - INFO - Epoch  10/10: Train Loss: 0.802820, Val Loss: 0.796918, Weighted Loss: 0.801050 (Best✓)
2025-06-26 12:12:20,005 - models.augmentation_factory - INFO - CDDPM训练完成
2025-06-26 12:12:21,049 - common.visualization - INFO - 训练曲线已保存: results\cddpm_training_curves.png
2025-06-26 12:12:21,050 - __main__ - INFO - ==================================================
2025-06-26 12:12:21,050 - __main__ - INFO - 生成增强样本
2025-06-26 12:12:21,050 - __main__ - INFO - ==================================================
2025-06-26 12:12:22,442 - models.augmentation_factory - INFO - CDDPM模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 12:12:22,442 - models.augmentation_factory - INFO - CDDPM 模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 12:12:22,444 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 12:12:22,445 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 12:12:22,445 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 12:12:22,445 - models.cddpm - INFO -   类别数量: 8
2025-06-26 12:12:22,772 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 12:12:22,773 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 12:12:22,773 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 12:12:22,777 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 12:12:24,021 - __main__ - INFO - 扩散模型已加载: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 12:12:24,022 - __main__ - INFO - 扩散模型已加载: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 12:12:24,022 - __main__ - WARNING - 配置中的 num_samples_per_class 是一个列表，自动取其第一个元素。值: [3]
2025-06-26 12:12:24,022 - __main__ - INFO - 开始使用扩散模型生成样本，每个类别 3 个...
2025-06-26 12:12:24,022 - __main__ - INFO - 生成故障样本+健康样本，健康样本标签=0，每类生成3个
2025-06-26 12:12:24,022 - __main__ - INFO - 生成类别 0 的样本...
2025-06-26 12:13:57,107 - __main__ - INFO - 生成类别 1 的样本...
2025-06-26 12:15:47,190 - __main__ - INFO - 生成类别 2 的样本...
2025-06-26 12:17:26,144 - __main__ - INFO - 生成类别 3 的样本...
2025-06-26 12:18:16,126 - __main__ - INFO - 生成类别 4 的样本...
2025-06-26 12:18:32,575 - __main__ - INFO - 生成类别 5 的样本...
2025-06-26 12:18:50,314 - __main__ - INFO - 生成类别 6 的样本...
2025-06-26 12:19:07,930 - __main__ - INFO - 生成类别 7 的样本...
2025-06-26 12:19:24,618 - __main__ - INFO - 样本生成完成，总共生成 24 个样本
2025-06-26 12:19:24,621 - __main__ - INFO - 生成样本已保存:
2025-06-26 12:19:24,621 - __main__ - INFO -   数据: generated_samples/KAT\CDDPM\generated_data.npy
2025-06-26 12:19:24,621 - __main__ - INFO -   标签: generated_samples/KAT\CDDPM\generated_labels.npy
2025-06-26 12:19:29,035 - common.visualization - INFO - 信号样本图已保存: results\generated_samples.png
2025-06-26 12:19:29,035 - __main__ - INFO - ==================================================
2025-06-26 12:19:29,035 - __main__ - INFO - 创建组合数据集（真实样本 + 增强样本）
2025-06-26 12:19:29,035 - __main__ - INFO - ==================================================
2025-06-26 12:19:29,037 - __main__ - INFO - 完整原始训练数据: 24 样本
2025-06-26 12:19:29,037 - __main__ - INFO - 生成数据: 24 样本
2025-06-26 12:19:29,037 - __main__ - INFO - 数据分布分析:
2025-06-26 12:19:29,038 - __main__ - INFO -   完整原始数据类别分布: {0: 3, 1: 3, 2: 3, 3: 3, 4: 3, 5: 3, 6: 3, 7: 3}
2025-06-26 12:19:29,038 - __main__ - INFO -   生成数据类别分布: {0: 3, 1: 3, 2: 3, 3: 3, 4: 3, 5: 3, 6: 3, 7: 3}
2025-06-26 12:19:29,038 - __main__ - INFO - 健康样本处理逻辑:
2025-06-26 12:19:29,038 - __main__ - INFO -   只生成故障样本: False
2025-06-26 12:19:29,038 - __main__ - INFO -   使用真实健康样本: True
2025-06-26 12:19:29,038 - __main__ - INFO -   真实健康样本数量配置: -1
2025-06-26 12:19:29,039 - __main__ - INFO -   检测到生成的健康样本，使用生成的健康样本
2025-06-26 12:19:29,039 - __main__ - INFO - 最终组合数据类别分布: {0: 6, 1: 6, 2: 6, 3: 6, 4: 6, 5: 6, 6: 6, 7: 6}
2025-06-26 12:19:29,039 - __main__ - INFO - 组合数据集总样本数: 48
2025-06-26 12:19:29,041 - __main__ - ERROR - 实验失败: keys must be str, int, float, bool or None, not int64
Traceback (most recent call last):
  File "e:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1888, in main
    results = run_comparison_experiments(args.config)
  File "e:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1653, in run_comparison_experiments
    return run_single_experiment(base_config)
  File "e:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1859, in run_single_experiment
    results = run_experiment(config)
  File "e:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1363, in run_experiment
    combined_data_info = save_combined_dataset(combined_data, combined_labels, config)
  File "e:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1025, in save_combined_dataset
    json.dump(info, f, indent=2, ensure_ascii=False)
  File "C:\Users\<USER>\anaconda3\envs\pytorch\lib\json\__init__.py", line 179, in dump
    for chunk in iterable:
  File "C:\Users\<USER>\anaconda3\envs\pytorch\lib\json\encoder.py", line 431, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Users\<USER>\anaconda3\envs\pytorch\lib\json\encoder.py", line 405, in _iterencode_dict
    yield from chunks
  File "C:\Users\<USER>\anaconda3\envs\pytorch\lib\json\encoder.py", line 376, in _iterencode_dict
    raise TypeError(f'keys must be str, int, float, bool or None, '
TypeError: keys must be str, int, float, bool or None, not int64
