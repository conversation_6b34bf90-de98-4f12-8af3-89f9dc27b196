2025-06-26 12:11:29,684 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\cddpm_fault_diagnosis_20250626_121129_20250626_121129.log
2025-06-26 12:11:29,685 - __main__ - INFO - ================================================================================
2025-06-26 12:11:29,685 - __main__ - INFO - 开始单一实验: cddpm_fault_diagnosis
2025-06-26 12:11:29,685 - __main__ - INFO - 数据集: KAT
2025-06-26 12:11:29,685 - __main__ - INFO - ================================================================================
2025-06-26 12:11:29,685 - __main__ - INFO - 🚀 实验开始
2025-06-26 12:11:29,685 - __main__ - INFO - ================================================================================
2025-06-26 12:11:29,685 - __main__ - INFO - 当前实验配置
2025-06-26 12:11:29,685 - __main__ - INFO - ================================================================================
2025-06-26 12:11:29,685 - __main__ - INFO - 数据集: KAT
2025-06-26 12:11:29,685 - __main__ - INFO - 故障样本每类: [3]
2025-06-26 12:11:29,686 - __main__ - INFO - 健康样本总数: -1
2025-06-26 12:11:29,686 - __main__ - INFO - 信号长度: 1024
2025-06-26 12:11:29,686 - __main__ - INFO - 归一化方法: minmax
2025-06-26 12:11:29,686 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 12:11:29,686 - __main__ - INFO - 每类生成样本数: [3]
2025-06-26 12:11:29,686 - __main__ - INFO - 只生成故障样本: False
2025-06-26 12:11:29,686 - __main__ - INFO - 扩散模型训练轮数: 10
2025-06-26 12:11:29,686 - __main__ - INFO - 分类器训练轮数: 100
2025-06-26 12:11:29,686 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 12:11:29,686 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 12:11:29,686 - __main__ - INFO - 设备: auto
2025-06-26 12:11:29,687 - __main__ - INFO - 性能模式: high_performance
2025-06-26 12:11:29,687 - __main__ - INFO - 随机种子: 42
2025-06-26 12:11:29,687 - __main__ - INFO - ================================================================================
2025-06-26 12:11:29,687 - __main__ - INFO - ============================================================
2025-06-26 12:11:29,687 - __main__ - INFO - 健康样本配置验证
2025-06-26 12:11:29,687 - __main__ - INFO - ============================================================
2025-06-26 12:11:29,687 - __main__ - INFO - 扩散训练健康样本数量: -1
2025-06-26 12:11:29,687 - __main__ - INFO - 只生成故障样本: False
2025-06-26 12:11:29,687 - __main__ - INFO - 分类器使用真实健康样本: True
2025-06-26 12:11:29,687 - __main__ - INFO - 真实健康样本数量: -1
2025-06-26 12:11:29,687 - __main__ - INFO - ✅ 健康样本配置验证通过
2025-06-26 12:11:29,688 - __main__ - INFO - ============================================================
2025-06-26 12:11:29,688 - __main__ - INFO - 使用设备: cuda
2025-06-26 12:11:29,689 - __main__ - INFO - 加载数据...
2025-06-26 12:11:29,689 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 12:11:29,689 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 12:11:29,692 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 12:11:29,696 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 12:11:29,696 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 12:11:29,696 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 12:11:29,696 - common.data_loader - INFO - 健康样本数量设置为-1，自动匹配故障样本数量: 3
2025-06-26 12:11:29,696 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 12:11:29,696 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 12:11:29,696 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:11:29,697 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:11:29,697 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:11:29,697 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:11:29,697 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:11:29,697 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:11:29,697 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 12:11:29,697 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:11:29,702 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 12:11:29,707 - common.data_loader - INFO - 数据加载完成:
2025-06-26 12:11:29,708 - common.data_loader - INFO -   训练样本: 24
2025-06-26 12:11:29,708 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 12:11:29,708 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 12:11:29,708 - common.data_loader - INFO -   类别数: 8
2025-06-26 12:11:29,710 - __main__ - INFO - ==================================================
2025-06-26 12:11:29,710 - __main__ - INFO - 开始训练数据增强模型
2025-06-26 12:11:29,710 - __main__ - INFO - ==================================================
2025-06-26 12:11:29,711 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-26 12:11:29,711 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 12:11:29,712 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 12:11:29,712 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 12:11:29,712 - models.cddpm - INFO -   类别数量: 8
2025-06-26 12:11:29,921 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 12:11:29,921 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 12:11:29,922 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 12:11:29,922 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 12:11:30,259 - __main__ - INFO - 数据增强方法已初始化: CDDPM
2025-06-26 12:11:30,259 - models.augmentation_factory - INFO - 开始训练 CDDPM 模型...
2025-06-26 12:11:30,260 - models.augmentation_factory - INFO - 开始训练CDDPM模型，共10轮
